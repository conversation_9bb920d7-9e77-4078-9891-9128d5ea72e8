'use client'

import { useRouter } from 'next/navigation'
import { useEffect, useMemo, useState } from 'react'
import { BookStoreLayout } from '../../components/BookStore/Layout'
import { SEO } from '../../components/SEO'
import { booksApi, bookCategoriesApi } from '../../services/bookstore'
import { Book, BookCategory } from '../../types/common'
import Image from 'next/image'

export default function BookStorePage() {
  const router = useRouter()
  const [books, setBooks] = useState<Book[]>([])
  const [categories, setCategories] = useState<BookCategory[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedCategory, setSelectedCategory] = useState('')
  const [sortBy, setSortBy] = useState('name')
  const [currentPage, setCurrentPage] = useState(1)
  const itemsPerPage = 12

  // Fetch data from API
  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true)
        setError(null)
        const [booksData, categoriesData] = await Promise.all([
          booksApi.getAllNoPagination(),
          bookCategoriesApi.getAll(),
        ])
        setBooks(
          booksData.filter(
            (book) => book.status === 'available' && !book.isDeleted,
          ),
        )
        setCategories(categoriesData.filter((cat) => cat.isActive))
      } catch (err) {
        setError('Không thể tải dữ liệu sách')
        console.error('Error fetching books:', err)
      } finally {
        setLoading(false)
      }
    }

    fetchData()
  }, [])

  // Get URL parameters
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search)
    const category = urlParams.get('category')
    const search = urlParams.get('search')

    if (category) setSelectedCategory(category)
    if (search) setSearchQuery(search)
  }, [])

  // Filter and sort books
  const filteredBooks = useMemo(() => {
    let filtered = books

    // Filter by search query
    if (searchQuery) {
      filtered = filtered.filter(
        (book) =>
          book.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
          book.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
          book.bookCategoryName
            ?.toLowerCase()
            .includes(searchQuery.toLowerCase()),
      )
    }

    // Filter by category
    if (selectedCategory) {
      filtered = filtered.filter(
        (book) => book.bookCategoryId === selectedCategory,
      )
    }

    // Sort books
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'name':
          return a.name.localeCompare(b.name)
        case 'price-low':
          return a.price - b.price
        case 'price-high':
          return b.price - a.price
        case 'rating':
          return (b.averageRating || 0) - (a.averageRating || 0)
        default:
          return 0
      }
    })

    return filtered
  }, [books, searchQuery, selectedCategory, sortBy])

  // Pagination
  const totalPages = Math.ceil(filteredBooks.length / itemsPerPage)
  const startIndex = (currentPage - 1) * itemsPerPage
  const paginatedBooks = filteredBooks.slice(
    startIndex,
    startIndex + itemsPerPage,
  )

  const handleSearch = (query: string) => {
    setSearchQuery(query)
    setCurrentPage(1)

    // Update URL
    const url = new URL(window.location.href)
    if (query) {
      url.searchParams.set('search', query)
    } else {
      url.searchParams.delete('search')
    }
    router.push(url.pathname + url.search)
  }

  const handleCategoryChange = (category: string) => {
    setSelectedCategory(category)
    setCurrentPage(1)

    // Update URL
    const url = new URL(window.location.href)
    if (category) {
      url.searchParams.set('category', category)
    } else {
      url.searchParams.delete('category')
    }
    router.push(url.pathname + url.search)
  }

  return (
    <>
      <SEO
        title="Sách giáo khoa"
        description="Khám phá bộ sưu tập sách giáo khoa chất lượng cao từ lớp 6 đến lớp 12 với giá cả hợp lý nhất."
      />
      <BookStoreLayout>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
            {/* Sidebar */}
            <div className="lg:col-span-1">
              <div className="bg-white rounded-lg shadow-md p-6">
                <h3 className="text-lg font-semibold mb-4">Tìm kiếm</h3>
                <input
                  type="text"
                  placeholder="Tìm kiếm sách..."
                  value={searchQuery}
                  onChange={(e) => handleSearch(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />

                <h3 className="text-lg font-semibold mb-4 mt-6">Danh mục</h3>
                <div className="space-y-2">
                  <button
                    onClick={() => handleCategoryChange('')}
                    className={`w-full text-left px-3 py-2 rounded-md transition-colors ${
                      selectedCategory === ''
                        ? 'bg-blue-100 text-blue-700'
                        : 'hover:bg-gray-100'
                    }`}
                  >
                    Tất cả
                  </button>
                  {categories.map((category) => (
                    <button
                      key={category.id}
                      onClick={() => handleCategoryChange(category.id)}
                      className={`w-full text-left px-3 py-2 rounded-md transition-colors ${
                        selectedCategory === category.id
                          ? 'bg-blue-100 text-blue-700'
                          : 'hover:bg-gray-100'
                      }`}
                    >
                      {category.name}
                    </button>
                  ))}
                </div>

                <h3 className="text-lg font-semibold mb-4 mt-6">Sắp xếp</h3>
                <select
                  value={sortBy}
                  onChange={(e) => setSortBy(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="name">Tên sách</option>
                  <option value="price-low">Giá tăng dần</option>
                  <option value="price-high">Giá giảm dần</option>
                  <option value="rating">Đánh giá cao nhất</option>
                </select>
              </div>
            </div>

            {/* Main content */}
            <div className="lg:col-span-3">
              <div className="flex justify-between items-center mb-6">
                <h1 className="text-2xl font-bold text-gray-900">
                  Sách giáo khoa ({filteredBooks.length} sản phẩm)
                </h1>
              </div>

              {loading ? (
                <div className="text-center py-16">
                  <div className="animate-spin rounded-full h-16 w-16 border-b-4 border-[#007A9C] mx-auto"></div>
                  <p className="mt-6 text-gray-600 text-lg">Đang tải sách...</p>
                  <div className="mt-4 flex justify-center space-x-1">
                    <div className="w-2 h-2 bg-[#007A9C] rounded-full animate-bounce"></div>
                    <div
                      className="w-2 h-2 bg-[#007A9C] rounded-full animate-bounce"
                      style={{ animationDelay: '0.1s' }}
                    ></div>
                    <div
                      className="w-2 h-2 bg-[#007A9C] rounded-full animate-bounce"
                      style={{ animationDelay: '0.2s' }}
                    ></div>
                  </div>
                </div>
              ) : error ? (
                <div className="text-center py-16">
                  <div className="mb-4">
                    <svg
                      className="w-16 h-16 text-red-500 mx-auto"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                      />
                    </svg>
                  </div>
                  <p className="text-red-600 text-lg font-medium mb-2">
                    Có lỗi xảy ra
                  </p>
                  <p className="text-gray-600 mb-6">{error}</p>
                  <button
                    onClick={() => window.location.reload()}
                    className="bg-gradient-to-r from-[#007A9C] to-[#005c73] text-white px-6 py-3 rounded-lg hover:from-[#006b87] hover:to-[#004d5f] transition-all duration-200 shadow-lg hover:shadow-xl"
                  >
                    <svg
                      className="w-4 h-4 inline mr-2"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
                      />
                    </svg>
                    Thử lại
                  </button>
                </div>
              ) : paginatedBooks.length > 0 ? (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {paginatedBooks.map((book) => (
                    <div
                      key={book.id}
                      className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow"
                    >
                      {book.imagePath ? (
                        <Image
                          src={book.imagePath}
                          alt={book.name}
                          className="w-full h-48 object-cover"
                          width={300}
                          height={200}
                        />
                      ) : (
                        <div className="w-full h-48 bg-gray-200 flex items-center justify-center">
                          <span className="text-gray-500">
                            Không có hình ảnh
                          </span>
                        </div>
                      )}
                      <div className="p-4">
                        <h3 className="text-lg font-semibold text-gray-900 mb-2">
                          {book.name}
                        </h3>
                        <p className="text-gray-600 text-sm mb-2">
                          Danh mục: {book.bookCategoryName}
                        </p>
                        <p className="text-gray-500 text-sm mb-3 line-clamp-2">
                          {book.description}
                        </p>
                        <div className="flex justify-between items-center mb-2">
                          <span className="text-xl font-bold text-blue-600">
                            {book.price.toLocaleString('vi-VN')}đ
                          </span>
                          {book.averageRating && (
                            <div className="flex items-center text-sm text-gray-600">
                              <span className="text-yellow-400">★</span>
                              <span className="ml-1">
                                {book.averageRating.toFixed(1)}
                              </span>
                            </div>
                          )}
                        </div>
                        <button className="w-full bg-gradient-to-r from-[#007A9C] to-[#005c73] text-white px-4 py-2 rounded-md hover:from-[#006b87] hover:to-[#004d5f] transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5">
                          <svg
                            className="w-4 h-4 inline mr-2"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-2.5 5M7 13l2.5 5m0 0h7M17 18a2 2 0 11-4 0 2 2 0 014 0zM9 18a2 2 0 11-4 0 2 2 0 014 0z"
                            />
                          </svg>
                          Thêm vào giỏ
                        </button>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-12">
                  <p className="text-gray-500 text-lg">
                    Không tìm thấy sách phù hợp
                  </p>
                </div>
              )}

              {/* Pagination */}
              {totalPages > 1 && (
                <div className="flex justify-center mt-8">
                  <div className="flex space-x-2">
                    <button
                      onClick={() => setCurrentPage(currentPage - 1)}
                      disabled={currentPage === 1}
                      className="px-3 py-2 border border-gray-300 rounded-md disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
                    >
                      Trước
                    </button>
                    {Array.from({ length: totalPages }, (_, i) => i + 1).map(
                      (page) => (
                        <button
                          key={page}
                          onClick={() => setCurrentPage(page)}
                          className={`px-3 py-2 border rounded-md ${
                            currentPage === page
                              ? 'bg-blue-600 text-white border-blue-600'
                              : 'border-gray-300 hover:bg-gray-50'
                          }`}
                        >
                          {page}
                        </button>
                      ),
                    )}
                    <button
                      onClick={() => setCurrentPage(currentPage + 1)}
                      disabled={currentPage === totalPages}
                      className="px-3 py-2 border border-gray-300 rounded-md disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
                    >
                      Sau
                    </button>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </BookStoreLayout>
    </>
  )
}
