import { ReactNode } from 'react'

export type WithChildren<T = {}> = T & { children: ReactNode }

// API Types
export interface User {
  id: number
  name: string
  avatar: string
  email: string
  title: string
  department: string
  role: string
  status: string
}

// Program Types
export interface Program {
  id: string
  name: string
  description: string
  isActive: boolean
  createdAt: string
  updatedAt: string
}

export interface CreateProgramRequest {
  name: string
  description: string
  isActive: boolean
}

export interface UpdateProgramRequest {
  name?: string
  description?: string
  isActive?: boolean
}

// Book Category Types
export interface BookCategory {
  id: string
  name: string
  description: string
  programId: string
  programName?: string
  isActive: boolean
  createdAt: string
  updatedAt: string
}

export interface CreateBookCategoryRequest {
  name: string
  description: string
  programId: string
  isActive: boolean
}

export interface UpdateBookCategoryRequest {
  name?: string
  description?: string
  isActive?: boolean
}

// Book Types
export interface Book {
  id: string
  name: string
  description: string
  price: number
  bookCategoryId: string
  bookCategoryName?: string
  programName?: string
  imagePath?: string
  filePath?: string
  status: 'available' | 'unavailable' | 'coming_soon'
  averageRating?: number
  ratingsCount?: number
  isDeleted?: boolean
  createdAt: string
  updatedAt: string
}

export interface CreateBookRequest {
  name: string
  description: string
  price: number
  bookCategoryId: string
  imagePath?: string
  filePath?: string
  status: 'available' | 'unavailable' | 'coming_soon'
}

export interface UpdateBookRequest {
  name?: string
  description?: string
  price?: number
  bookCategoryId?: string
  imagePath?: string
  filePath?: string
  status?: 'available' | 'unavailable' | 'coming_soon'
}

// API Response Types
export interface ApiResponse<T> {
  data: T
  message?: string
  success?: boolean
}

export interface PaginatedResponse<T> {
  data: T[]
  meta: {
    page: number
    take: number
    itemCount: number
    pageCount: number
    hasPreviousPage: boolean
    hasNextPage: boolean
  }
}

// Query Parameters
export interface BookQueryParams {
  page?: number
  take?: number
  order?: 'ASC' | 'DESC'
  q?: string
  status?: 'available' | 'unavailable' | 'coming_soon'
  categoryId?: string
  search?: string
  minPrice?: number
  maxPrice?: number
}
