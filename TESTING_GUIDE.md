# Testing Guide - IBBook Frontend

## Tổng quan
Hướng dẫn này mô tả cách test các chức năng đã được cập nhật trong IBBook Frontend sau khi kết nối với backend API thật.

## Cấu hình Backend
Đảm bảo backend đang chạy trên `http://localhost:3001` (hoặc cập nhật `NEXT_PUBLIC_API_URL` trong `.env.local`)

```bash
# Trong file .env.local
NEXT_PUBLIC_API_URL=http://localhost:3001
```

## Các chức năng cần test

### 1. Admin Programs Management
**Đường dẫn:** `/admin/programs`

**Test cases:**
- [ ] Hiển thị danh sách programs từ API
- [ ] Tạo program mới với form validation
- [ ] Sửa program hiện có
- [ ] Xóa program với confirmation
- [ ] Tìm kiếm programs
- [ ] Loading states và error handling

**Dữ liệu test:**
```json
{
  "name": "IB Mathematics Test",
  "description": "Test program for IB Mathematics",
  "isActive": true
}
```

### 2. Admin Categories Management
**Đường dẫn:** `/admin/categories`

**Test cases:**
- [ ] Hiển thị danh sách categories từ API
- [ ] Tạo category mới với program selection
- [ ] Sửa category hiện có
- [ ] Xóa category với confirmation
- [ ] Filter theo program
- [ ] Tìm kiếm categories

**Dữ liệu test:**
```json
{
  "name": "Analysis and Approaches Test",
  "description": "Test category for mathematical analysis",
  "programId": "program-uuid-here",
  "isActive": true
}
```

### 3. Admin Books Management
**Đường dẫn:** `/admin/books`

**Test cases:**
- [ ] Hiển thị danh sách books từ API
- [ ] Tạo book mới với category selection
- [ ] Sửa book hiện có
- [ ] Xóa book (soft delete)
- [ ] Filter theo category và status
- [ ] Tìm kiếm books
- [ ] Hiển thị rating và reviews

**Dữ liệu test:**
```json
{
  "name": "Test Mathematics Book",
  "description": "A comprehensive test book for mathematics",
  "price": 299000,
  "bookCategoryId": "category-uuid-here",
  "imagePath": "/images/test-book.jpg",
  "filePath": "/files/test-book.pdf",
  "status": "available"
}
```

### 4. Frontend BookStore
**Đường dẫn:** `/bookstore`

**Test cases:**
- [ ] Hiển thị danh sách sách available từ API
- [ ] Filter theo categories
- [ ] Tìm kiếm sách
- [ ] Sắp xếp theo tên, giá, rating
- [ ] Pagination
- [ ] Hiển thị thông tin sách (tên, giá, rating, category)
- [ ] Loading states và error handling

## Kiểm tra UI/UX Improvements

### Design System
- [ ] Màu chủ đạo #007A9C được áp dụng nhất quán
- [ ] Gradient buttons với hover effects
- [ ] Icons trong buttons và actions
- [ ] Loading animations với brand colors
- [ ] Error states với icons và retry buttons
- [ ] Responsive design trên mobile/tablet

### Interactions
- [ ] Hover effects trên buttons và cards
- [ ] Smooth transitions (200ms duration)
- [ ] Shadow effects (shadow-lg, hover:shadow-xl)
- [ ] Transform effects (hover:-translate-y-0.5)
- [ ] Focus states với brand colors

## API Integration Testing

### Error Scenarios
Test các trường hợp lỗi:
- [ ] Backend không khả dụng (500 error)
- [ ] Network timeout
- [ ] Invalid data submission
- [ ] Unauthorized access (401)
- [ ] Validation errors (400)

### Performance
- [ ] Loading states hiển thị ngay lập tức
- [ ] Data caching (nếu có)
- [ ] Optimistic updates (nếu có)

## Browser Compatibility
Test trên các trình duyệt:
- [ ] Chrome (latest)
- [ ] Firefox (latest)
- [ ] Safari (latest)
- [ ] Edge (latest)

## Mobile Testing
- [ ] Responsive layout trên mobile
- [ ] Touch interactions
- [ ] Mobile navigation

## Checklist hoàn thành
- [ ] Tất cả CRUD operations hoạt động
- [ ] UI/UX improvements được áp dụng
- [ ] Error handling hoạt động tốt
- [ ] Loading states hiển thị đúng
- [ ] Responsive design hoạt động
- [ ] Brand colors được áp dụng nhất quán

## Báo cáo lỗi
Nếu phát hiện lỗi, vui lòng ghi chú:
1. Bước tái tạo lỗi
2. Kết quả mong đợi vs thực tế
3. Browser và device
4. Screenshots (nếu có)
