import axios, { AxiosResponse } from 'axios'
import { 
  Program, 
  BookCategory, 
  Book, 
  CreateProgramRequest, 
  UpdateProgramRequest,
  CreateBookCategoryRequest,
  UpdateBookCategoryRequest,
  CreateBookRequest,
  UpdateBookRequest,
  ApiResponse,
  PaginatedResponse,
  BookQueryParams
} from '../types/common'

// Create axios instance
const api = axios.create({
  baseURL: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001',
  headers: {
    'Content-Type': 'application/json',
  },
})

// Request interceptor to add auth token
api.interceptors.request.use((config) => {
  const token = localStorage.getItem('accessToken')
  if (token) {
    config.headers.Authorization = `Bearer ${token}`
  }
  return config
})

// Response interceptor for error handling
api.interceptors.response.use(
  (response) => response,
  (error) => {
    console.error('API Error:', error.response?.data || error.message)
    return Promise.reject(error)
  }
)

// Programs API
export const programsApi = {
  // Get all programs (Public)
  getAll: async (): Promise<Program[]> => {
    const response: AxiosResponse<Program[]> = await api.get('/programs')
    return response.data
  },

  // Create program (Admin only)
  create: async (data: CreateProgramRequest): Promise<Program> => {
    const response: AxiosResponse<Program> = await api.post('/programs', data)
    return response.data
  },

  // Update program (Admin only)
  update: async (id: string, data: UpdateProgramRequest): Promise<Program> => {
    const response: AxiosResponse<Program> = await api.put(`/programs/${id}`, data)
    return response.data
  },

  // Delete program (Admin only)
  delete: async (id: string): Promise<void> => {
    await api.delete(`/programs/${id}`)
  },
}

// Book Categories API
export const bookCategoriesApi = {
  // Get all book categories (Public)
  getAll: async (): Promise<BookCategory[]> => {
    const response: AxiosResponse<BookCategory[]> = await api.get('/book-categories')
    return response.data
  },

  // Create book category (Admin only)
  create: async (data: CreateBookCategoryRequest): Promise<BookCategory> => {
    const response: AxiosResponse<BookCategory> = await api.post('/book-categories', data)
    return response.data
  },

  // Update book category (Admin only)
  update: async (id: string, data: UpdateBookCategoryRequest): Promise<BookCategory> => {
    const response: AxiosResponse<BookCategory> = await api.put(`/book-categories/${id}`, data)
    return response.data
  },

  // Delete book category (Admin only)
  delete: async (id: string): Promise<void> => {
    await api.delete(`/book-categories/${id}`)
  },
}

// Books API
export const booksApi = {
  // Get books with pagination and filters (Public)
  getAll: async (params?: BookQueryParams): Promise<PaginatedResponse<Book>> => {
    const queryParams = new URLSearchParams()
    
    if (params?.page) queryParams.append('page', params.page.toString())
    if (params?.take) queryParams.append('take', params.take.toString())
    if (params?.order) queryParams.append('order', params.order)
    if (params?.q) queryParams.append('q', params.q)
    if (params?.status) queryParams.append('status', params.status)
    if (params?.categoryId) queryParams.append('categoryId', params.categoryId)
    if (params?.search) queryParams.append('search', params.search)
    if (params?.minPrice) queryParams.append('minPrice', params.minPrice.toString())
    if (params?.maxPrice) queryParams.append('maxPrice', params.maxPrice.toString())

    const response: AxiosResponse<PaginatedResponse<Book>> = await api.get(
      `/books?${queryParams.toString()}`
    )
    return response.data
  },

  // Get all books without pagination (Public)
  getAllNoPagination: async (): Promise<Book[]> => {
    const response: AxiosResponse<Book[]> = await api.get('/books/all')
    return response.data
  },

  // Get single book by ID
  getById: async (id: string): Promise<Book> => {
    const response: AxiosResponse<Book> = await api.get(`/books/${id}`)
    return response.data
  },

  // Create book (Admin only)
  create: async (data: CreateBookRequest): Promise<Book> => {
    const response: AxiosResponse<Book> = await api.post('/books', data)
    return response.data
  },

  // Update book (Admin only)
  update: async (id: string, data: UpdateBookRequest): Promise<Book> => {
    const response: AxiosResponse<Book> = await api.put(`/books/${id}`, data)
    return response.data
  },

  // Delete book - Soft delete (Admin only)
  delete: async (id: string): Promise<void> => {
    await api.delete(`/books/${id}`)
  },
}

// Combined export
export const bookstoreApi = {
  programs: programsApi,
  categories: bookCategoriesApi,
  books: booksApi,
}

export default bookstoreApi
