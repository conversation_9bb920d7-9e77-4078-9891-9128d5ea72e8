'use client'

import { useRouter } from 'next/navigation'
import { useEffect, useState } from 'react'
import { BookStoreLayout } from '../../components/BookStore/Layout'
import { SEO } from '../../components/SEO'

export default function OrderSuccessPage() {
  const router = useRouter()
  const [orderId, setOrderId] = useState<string | null>(null)

  useEffect(() => {
    // Get order ID from URL params
    const urlParams = new URLSearchParams(window.location.search)
    const id = urlParams.get('orderId')
    setOrderId(id)
  }, [])

  return (
    <>
      <SEO
        title="Đặt hàng thành công"
        description="Cảm ơn bạn đã đặt hàng. Đơn hàng của bạn đã được xác nhận."
      />
      <BookStoreLayout>
        <div className="max-w-4xl mx-auto px-4 py-16">
          <div className="text-center">
            {/* Success Icon */}
            <div className="w-24 h-24 mx-auto mb-8 bg-green-100 rounded-full flex items-center justify-center">
              <svg
                className="w-12 h-12 text-green-600"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M5 13l4 4L19 7"
                />
              </svg>
            </div>

            {/* Title and Message */}
            <h1 className="text-4xl font-bold text-green-600 mb-4">
              Đặt hàng thành công!
            </h1>

            <p className="text-xl text-gray-600 mb-8">
              Cảm ơn bạn đã đặt hàng. Đơn hàng của bạn đã được xác nhận và đang
              được xử lý.
            </p>

            {/* Order Details */}
            {orderId && (
              <div className="bg-gray-50 rounded-lg p-6 mb-8 max-w-md mx-auto">
                <h3 className="font-semibold text-gray-900 mb-4">
                  Thông tin đơn hàng
                </h3>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Mã đơn hàng:</span>
                    <span className="font-mono font-semibold">#{orderId}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Trạng thái:</span>
                    <span className="font-semibold text-green-600">
                      Đã xác nhận
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Ngày đặt:</span>
                    <span>{new Date().toLocaleDateString('vi-VN')}</span>
                  </div>
                </div>
              </div>
            )}

            {/* Next Steps */}
            <div className="bg-blue-50 rounded-lg p-6 mb-8 max-w-2xl mx-auto text-left">
              <h3 className="font-semibold text-blue-900 mb-4">
                Những bước tiếp theo:
              </h3>
              <div className="space-y-3 text-sm text-blue-800">
                <div className="flex items-start space-x-3">
                  <div className="w-6 h-6 bg-blue-200 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                    <span className="text-xs font-semibold">1</span>
                  </div>
                  <div>
                    <p className="font-medium">Xác nhận đơn hàng</p>
                    <p className="text-xs">
                      Chúng tôi sẽ gửi email xác nhận trong vòng 24 giờ
                    </p>
                  </div>
                </div>
                <div className="flex items-start space-x-3">
                  <div className="w-6 h-6 bg-blue-200 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                    <span className="text-xs font-semibold">2</span>
                  </div>
                  <div>
                    <p className="font-medium">Chuẩn bị hàng</p>
                    <p className="text-xs">
                      Đơn hàng sẽ được chuẩn bị và đóng gói cẩn thận
                    </p>
                  </div>
                </div>
                <div className="flex items-start space-x-3">
                  <div className="w-6 h-6 bg-blue-200 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                    <span className="text-xs font-semibold">3</span>
                  </div>
                  <div>
                    <p className="font-medium">Giao hàng</p>
                    <p className="text-xs">
                      Hàng sẽ được giao trong 2-5 ngày làm việc
                    </p>
                  </div>
                </div>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex flex-col sm:flex-row gap-4 justify-center mb-8">
              <button
                onClick={() => router.push('/bookstore')}
                className="bg-blue-600 text-white px-6 py-3 rounded-md hover:bg-blue-700 transition-colors"
              >
                Tiếp tục mua sắm
              </button>
              <button
                onClick={() => router.push('/')}
                className="border border-gray-300 text-gray-700 px-6 py-3 rounded-md hover:bg-gray-50 transition-colors"
              >
                Về trang chủ
              </button>
            </div>

            {/* Support Info */}
            <div className="p-6 bg-gray-50 rounded-lg max-w-md mx-auto">
              <h3 className="font-semibold text-gray-900 mb-2">Cần hỗ trợ?</h3>
              <p className="text-sm text-gray-600 mb-3">
                Nếu bạn có câu hỏi về đơn hàng, vui lòng liên hệ:
              </p>
              <div className="text-sm text-gray-700">
                <p>📞 Điện thoại: 0949351612</p>
                <p>📧 Email: <EMAIL></p>
                <p>💬 Zalo: IBBook</p>
              </div>
            </div>
          </div>
        </div>
      </BookStoreLayout>
    </>
  )
}
