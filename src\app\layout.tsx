import '../styles/index.css'
import { ReactNode } from 'react'
import { Inter } from 'next/font/google'
import { AdminAuthProvider } from '../context/adminAuth'
import { AuthProvider } from '../context/authContext'
import { CartProvider } from '../context/cart'
import { Toaster } from '../components/Toast'

const inter = Inter({
  subsets: ['latin'],
  weight: ['300', '400', '500', '600'],
  display: 'swap',
})

export const metadata = {
  title: 'IBBook - Chuyên sách tiếng Anh',
  description:
    'IBBook - Chuyên cung cấp sách tiếng <PERSON>h <PERSON>, IELTS, Oxford, Pearson với giá tốt nhất. Giao hàng toàn quốc, tặng kèm file nghe miễn phí.',
  openGraph: {
    title: 'IBBook - Chuyên sách tiếng Anh',
    description:
      'IBBook - Chuyên cung cấp sách tiếng <PERSON>, IELTS, Oxford, <PERSON> với giá tốt nhất. <PERSON>ia<PERSON> hàng toàn quốc, tặng kèm file nghe miễn phí.',
    images: ['/thumbnail.jpeg'],
  },
  twitter: {
    site: '@sachtienganhhanoi',
    card: 'summary_large_image',
    images: ['/thumbnail.jpeg'],
  },
  icons: {
    icon: '/favicon.ico',
    shortcut: '/favicon-16x16.png',
    apple: '/favicon-32x32.png',
  },
}

export const viewport = {
  themeColor: '#E13F5E',
}

export default function RootLayout({ children }: { children: ReactNode }) {
  return (
    <html lang="vi">
      <head>
        <link href="/favicon.ico" rel="icon" type="image/x-icon" />
        <link
          href="/favicon-16x16.png"
          rel="icon"
          sizes="16x16"
          type="image/png"
        />
        <link
          href="/favicon-32x32.png"
          rel="icon"
          sizes="32x32"
          type="image/png"
        />
        <link href="https://fonts.googleapis.com" rel="preconnect" />
        <link href="https://static.cloudflareinsights.com" rel="preconnect" />
        <link
          crossOrigin="anonymous"
          href="https://fonts.gstatic.com"
          rel="preconnect"
        />
      </head>
      <body className={inter.className}>
        <AuthProvider>
          <AdminAuthProvider>
            <CartProvider>{children}</CartProvider>
          </AdminAuthProvider>
        </AuthProvider>
        <Toaster />
      </body>
    </html>
  )
}
